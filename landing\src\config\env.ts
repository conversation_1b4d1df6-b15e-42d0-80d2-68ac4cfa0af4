import { createClient } from '@supabase/supabase-js';

// Environment variables
export const env = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
  }
};

// Validate required environment variables
if (!env.supabase.url || !env.supabase.serviceRoleKey) {
  throw new Error(
    'Missing required environment variables for Supabase. ' +
    'Please check NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY.'
  );
}

// Initialize Supabase client
export const supabase = createClient(
  env.supabase.url,
  env.supabase.serviceRoleKey
);

