"use client";

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Footer from "@/components/Footer"; // Import the Footer component

interface FAQItem {
  question: string;
  answer: string;
}

const patientFaqs: FAQItem[] = [
  {
    question: "What is Petals AI for patients?",
    answer: "Petals AI provides patients with tools to better understand their health data, access personalized health insights, connect with healthcare providers, and manage appointments more efficiently."
  },
  {
    question: "How can Petals AI help me manage my health?",
    answer: "You can use Petals AI to track your medical history, view lab results, receive medication reminders, and get AI-powered insights to help you make informed decisions about your health in consultation with your doctor."
  },
  {
    question: "Is my personal health information protected?",
    answer: "Absolutely. Petals AI is designed with strict adherence to privacy regulations like HIPAA. Your health data is encrypted, securely stored, and only accessible by you and authorized healthcare providers you choose."
  },
  {
    question: "Can Petals AI replace my doctor?",
    answer: "No, Petals AI is a supportive tool designed to augment healthcare, not replace professional medical advice or treatment. Always consult with your doctor for diagnosis and treatment plans."
  },
  {
    question: "How do I sign up as a patient?",
    answer: "You can join our patient early access list via the 'Join as Patient' link on our homepage or navigation menu. We will guide you through the process once early access opens."
  },
  {
    question: "What devices can I use Petals AI on?",
    answer: "Petals AI is accessible through web browsers on desktop and mobile devices. We are also working on dedicated mobile applications for an even better experience."
  }
];

export default function PatientFAQPage() {
  return (
    <>
      <div className="min-h-screen bg-[#EAF7F9] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-4xl font-bold text-center text-[#2E475D] mb-10">
            Patient Frequently Asked Questions
          </h1>
          <div className="space-y-6">
            {patientFaqs.map((faq, index) => (
              <Accordion key={index} type="single" collapsible className="w-full">
                <AccordionItem value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-lg font-semibold text-[#2E475D] hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-700 leading-relaxed px-4 py-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}