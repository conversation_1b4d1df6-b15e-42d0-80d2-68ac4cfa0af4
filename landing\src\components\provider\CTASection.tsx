"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ArrowRight, Calendar, Users, Zap, Shield } from "lucide-react";
import Link from "next/link";

const CTASection = () => {
  const ctaOptions = [
    {
      icon: <Calendar className="h-8 w-8 text-[#6A8E99]" />,
      title: "Schedule a Demo",
      description: "See P.E.T.A.L.S. AI in action with a personalized demo tailored to your practice",
      buttonText: "Book Demo",
      buttonLink: "/demo_request",
      isPrimary: true
    },
    {
      icon: <Zap className="h-8 w-8 text-[#6A8E99]" />,
      title: "Start Free Trial",
      description: "Try P.E.T.A.L.S. AI risk-free for 30 days with full access to all features",
      buttonText: "Start Trial",
      buttonLink: "/early_access",
      isPrimary: false
    },
    {
      icon: <Users className="h-8 w-8 text-[#6A8E99]" />,
      title: "Join Provider Beta",
      description: "Be among the first to experience our latest provider tools and features",
      buttonText: "Join Beta",
      buttonLink: "/early_access",
      isPrimary: false
    }
  ];

  const benefits = [
    "No setup fees or long-term contracts",
    "Full onboarding and training included",
    "24/7 clinical support team",
    "HIPAA-compliant from day one"
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-gradient-to-br from-[#EAF7F9] to-[#F2FFF6]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main CTA */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#2E475D] mb-6">
            Ready to Transform Your Practice?
          </h2>
          <p className="text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Join thousands of healthcare providers who are already using P.E.T.A.L.S. AI 
            to deliver better patient care, improve efficiency, and enhance clinical outcomes.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/demo_request">
              <Button className="bg-[#6A8E99] hover:bg-[#597984] text-white px-8 py-4 rounded-md text-lg font-semibold flex items-center gap-2">
                Schedule Your Demo
                <ArrowRight className="h-5 w-5" />
              </Button>
            </Link>
            <Link href="/early_access">
              <Button variant="outline" className="border-2 border-[#6A8E99] text-[#6A8E99] hover:bg-[#6A8E99] hover:text-white px-8 py-4 rounded-md text-lg font-semibold">
                Start Free Trial
              </Button>
            </Link>
          </div>

          {/* Benefits */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-w-4xl mx-auto">
            {benefits.map((benefit, index) => (
              <div key={index} className="flex items-center gap-3 justify-center md:justify-start">
                <Shield className="h-5 w-5 text-[#6A8E99] flex-shrink-0" />
                <span className="text-gray-700 text-sm">{benefit}</span>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Options */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {ctaOptions.map((option, index) => (
            <Card 
              key={index} 
              className={`bg-white shadow-sm hover:shadow-lg transition-all duration-300 ${
                option.isPrimary ? 'border-2 border-[#6A8E99]' : 'border border-gray-200'
              }`}
            >
              <CardContent className="p-8 text-center h-full flex flex-col">
                <div className="mb-4 flex justify-center">
                  {option.icon}
                </div>
                <h3 className="text-xl font-bold text-[#2E475D] mb-3">
                  {option.title}
                </h3>
                <p className="text-gray-600 mb-6 flex-grow">
                  {option.description}
                </p>
                <Link href={option.buttonLink}>
                  <Button 
                    className={`w-full py-3 rounded-md ${
                      option.isPrimary
                        ? 'bg-[#6A8E99] hover:bg-[#597984] text-white'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                    }`}
                  >
                    {option.buttonText}
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Contact Information */}
        <div className="bg-white rounded-2xl p-8 sm:p-12 shadow-sm border border-gray-100 text-center">
          <h3 className="text-2xl font-bold text-[#2E475D] mb-4">
            Need Help Choosing the Right Plan?
          </h3>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            Our healthcare technology specialists are here to help you find the perfect solution 
            for your practice. Get personalized recommendations and answers to all your questions.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-[#6A8E99] rounded-full"></div>
              <span className="text-gray-700">Call: 234-700-PETALS-AI</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-3 h-3 bg-[#6A8E99] rounded-full"></div>
              <span className="text-gray-700">Email: <EMAIL></span>
            </div>
          </div>
          
          <div className="mt-6">
            <Link href="/contact">
              <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] px-8 py-3 rounded-md">
                Contact Our Team
              </Button>
            </Link>
          </div>
        </div>

        {/* Final Stats */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">30 Days</div>
              <div className="text-gray-600">Free Trial Period</div>
            </div>
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">24/7</div>
              <div className="text-gray-600">Clinical Support</div>
            </div>
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">99.9%</div>
              <div className="text-gray-600">Uptime Guarantee</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
