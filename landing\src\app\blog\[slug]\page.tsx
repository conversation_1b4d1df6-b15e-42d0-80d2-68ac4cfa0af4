import Image from "next/image";
import Link from "next/link";
import { allBlogPosts } from "@/lib/blogData";

export default async function BlogDetailPage({ params }: { params: Promise<{ slug: string }> }) {
  const { slug } = await params;

  const blogPost = allBlogPosts.find((post) => post.slug === slug);

  if (!blogPost) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen py-12 bg-gray-50">
        <h1 className="text-4xl font-bold text-[#2E475D] mb-4">Blog Post Not Found</h1>
        <p className="text-lg text-gray-600 mb-8">
          The blog post you are looking for does not exist.
        </p>
        <Link href="/blog" className="text-[#6A8E99] hover:underline font-medium">
          Go back to Blogs
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 md:py-20">
      <div className="container mx-auto px-4 md:px-6 max-w-4xl bg-white rounded-lg shadow-lg overflow-hidden">
        <div className="relative w-full h-80 md:h-96">
          <Image
            src={blogPost.image}
            alt={blogPost.title}
            fill
            style={{ objectFit: 'cover' }}
            className="rounded-t-lg"
          />
        </div>
        <div className="p-6 md:p-8 lg:p-10">
          <h1 className="text-3xl md:text-4xl font-bold text-[#2E475D] mb-4">
            {blogPost.title}
          </h1>
          <p className="text-gray-500 text-sm mb-6">
            By {blogPost.author} on {blogPost.date}
          </p>
          <div
            className="prose prose-lg max-w-none text-gray-700 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: blogPost.content }}
          />
          <div className="mt-8">
            <Link href="/blog" className="text-[#6A8E99] hover:underline font-medium">
              &larr; Back to all Blogs
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

// Function to generate static paths for all blog posts
// This is important for Next.js to pre-render pages for dynamic routes
export async function generateStaticParams() {
  return allBlogPosts.map((post) => ({
    slug: post.slug,
  }));
}