"use client";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Heart, 
  Brain, 
  Bone, 
  Eye, 
  Stethoscope, 
  Baby,
  Users,
  Building2,
  UserCheck
} from "lucide-react";
import Link from "next/link";

const SolutionsSection = () => {
  const specialties = [
    {
      icon: <Heart className="h-8 w-8 text-[#6A8E99]" />,
      title: "Cardiology",
      description: "Advanced cardiac monitoring, risk assessment, and patient management tools."
    },
    {
      icon: <Brain className="h-8 w-8 text-[#6A8E99]" />,
      title: "Neurology",
      description: "Neurological symptom tracking, cognitive assessments, and treatment optimization."
    },
    {
      icon: <Bone className="h-8 w-8 text-[#6A8E99]" />,
      title: "Orthopedics",
      description: "Pain management, mobility tracking, and rehabilitation progress monitoring."
    },
    {
      icon: <Eye className="h-8 w-8 text-[#6A8E99]" />,
      title: "Ophthalmology",
      description: "Vision tracking, eye health monitoring, and treatment compliance tools."
    },
    {
      icon: <Stethoscope className="h-8 w-8 text-[#6A8E99]" />,
      title: "Internal Medicine",
      description: "Comprehensive chronic disease management and preventive care tools."
    },
    {
      icon: <Baby className="h-8 w-8 text-[#6A8E99]" />,
      title: "Pediatrics",
      description: "Child-friendly interfaces and family-centered care coordination."
    }
  ];

  const practiceTypes = [
    {
      icon: <UserCheck className="h-10 w-10 text-[#6A8E99]" />,
      title: "Solo Practice",
      description: "Streamlined tools for independent practitioners",
      features: ["Patient management", "AI clinical support", "Automated documentation", "Telehealth integration"]
    },
    {
      icon: <Users className="h-10 w-10 text-[#6A8E99]" />,
      title: "Group Practice",
      description: "Collaborative tools for multi-provider practices",
      features: ["Team collaboration", "Shared patient records", "Practice analytics", "Resource optimization"]
    },
    {
      icon: <Building2 className="h-10 w-10 text-[#6A8E99]" />,
      title: "Healthcare Systems",
      description: "Enterprise solutions for large organizations",
      features: ["Multi-site management", "Population health", "Quality reporting", "Enterprise integration"]
    }
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Specialties Section */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Solutions for Every Specialty
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              P.E.T.A.L.S. AI adapts to your specialty&apos;s unique needs with specialized tools 
              and workflows designed by healthcare professionals.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
            {specialties.map((specialty, index) => (
              <Card key={index} className="bg-[#F8FFFE] border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-6 text-center">
                  <div className="mb-4 flex justify-center">
                    {specialty.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-[#2E475D] mb-3">
                    {specialty.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {specialty.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Practice Types Section */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Tailored for Your Practice Size
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              From solo practitioners to large healthcare systems, our platform scales 
              to meet your specific needs and workflow requirements.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {practiceTypes.map((type, index) => (
              <Card key={index} className="bg-white border border-gray-200 shadow-sm hover:shadow-lg transition-all duration-300">
                <CardContent className="p-8">
                  <div className="mb-6 flex justify-center">
                    {type.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#2E475D] mb-3 text-center">
                    {type.title}
                  </h3>
                  <p className="text-gray-600 mb-6 text-center">
                    {type.description}
                  </p>
                  <ul className="space-y-3">
                    {type.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <div className="w-2 h-2 bg-[#6A8E99] rounded-full flex-shrink-0"></div>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="bg-[#F2FFF6] rounded-2xl p-8 sm:p-12 text-center">
          <h3 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
            Ready to Transform Your Practice?
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of healthcare providers who are already using P.E.T.A.L.S. AI 
            to deliver better patient care and improve practice efficiency.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/demo_request">
              <Button className="bg-[#6A8E99] hover:bg-[#597984] text-white px-8 py-3 rounded-md">
                Schedule a Demo
              </Button>
            </Link>
            <Link href="/early_access">
              <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] px-8 py-3 rounded-md">
                Start Free Trial
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionsSection;
