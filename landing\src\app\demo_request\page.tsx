"use client";

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import * as z from "zod";
import { format } from "date-fns";
import { CalendarIcon, ChevronLeft } from "lucide-react";
import { useState, useEffect, forwardRef } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import Image from "next/image";
import PhoneInput from 'react-phone-number-input';
import 'react-phone-number-input/style.css';

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Textarea } from "@/components/ui/textarea";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";

// Custom Input component for PhoneInput
const CustomPhoneInput = forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>((props, ref) => (
  <Input
    {...props}
    ref={ref}
    className="bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]"
  />
));
CustomPhoneInput.displayName = 'CustomPhoneInput';

// Form schema
const formSchema = z.object({
  fullName: z.string().min(2, { message: "Full name is required" }),
  email: z.string().email({ message: "Invalid email address" }),
  role: z.string({ required_error: "Please select a role" }),
  phone: z.string().min(7, { message: "Valid phone number is required" }),
  demoType: z.enum(["live", "video", "both"], {
    required_error: "Please select a demo type",
  }),
  preferredDate: z.date({
    required_error: "Please select a date",
  }),
  preferredTime: z.object(
    {
      hour: z.string(),
      minute: z.string(),
      period: z.enum(["AM", "PM"]),
    },
    {
      required_error: "Please select a time",
    }
  ),
  additionalInfo: z.string().optional(),
});

type FormValues = z.infer<typeof formSchema>;

// Success Message Component
const SuccessMessage = ({ message }: { message?: string }) => {
  return (
    <div className="flex flex-col items-center justify-center py-16 px-4 text-center">
      <Image
        src="/images/icon-park_email-successfully.svg"
        alt="Success"
        width={120}
        height={120}
        className="mb-8"
      />
      <h2 className="text-2xl font-semibold mb-4 text-[#2E475D]">
        Thank you! Your demo request has been received
      </h2>
      <p className="text-gray-600 max-w-md mx-auto mb-6">
        {message || "Our team will contact you shortly to schedule your session or send over a demo video"}
      </p>
      <p className="text-sm text-gray-500">
        Redirecting to home page in a few seconds...
      </p>
    </div>
  );
};

export default function DemoRequestPage() {
  const router = useRouter();
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [responseMessage, setResponseMessage] = useState<string | undefined>(undefined);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      fullName: "",
      email: "",
      role: "",
      phone: "",
      demoType: undefined,
      preferredTime: {
        hour: "09",
        minute: "00",
        period: "AM",
      },
      additionalInfo: "",
    },
  });

  // Handle redirect after successful submission
  useEffect(() => {
    let redirectTimer: NodeJS.Timeout;

    if (isSubmitted) {
      redirectTimer = setTimeout(() => {
        router.push("/");
      }, 10000);
    }

    return () => {
      if (redirectTimer) clearTimeout(redirectTimer);
    };
  }, [isSubmitted, router]);

  const onSubmit = async (values: FormValues) => {
    // Format time to 24-hour format for submission if needed
    const { hour, period } = values.preferredTime;
    let formattedHour = parseInt(hour);

    if (period === "PM" && formattedHour < 12) {
      formattedHour += 12;
    } else if (period === "AM" && formattedHour === 12) {
      formattedHour = 0;
    }

    // const formattedTime = `${formattedHour
    //   .toString()
    //   .padStart(2, "0")}:${minute}`;

    // Handle date timezone issue - create a new date with time set to noon
    // This prevents the date from shifting due to timezone conversions
    const selectedDate = new Date(values.preferredDate);
    selectedDate.setHours(12, 0, 0, 0);

    console.log('Submitting form data:', {
      ...values,
      preferredDate: selectedDate,
    });

    try {
      const response = await fetch('/api/demo_request', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...values,
          preferredDate: selectedDate.toISOString(), // Send as ISO string
        }),
      });

      console.log('Response status:', response.status);
      let data;
      
      try {
        data = await response.json();
        console.log('Response data:', data);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        data = { error: 'Failed to parse server response' };
      }

      if (response.ok) {
        if (data.message) {
          // This means user already exists
          setResponseMessage(data.message);
        }
        setIsSubmitted(true);
      } else {
        // Show error in form
        form.setError("root", { 
          type: "server", 
          message: data.error || "Failed to submit form. Please try again." 
        });
        console.error('Error submitting form:', data);
      }
    } catch (error) {
      // Show error in form
      console.error('Submission error details:', error);
      form.setError("root", { 
        type: "server", 
        message: "An unexpected error occurred. Please try again." 
      });
      console.error('Error submitting form:', error);
    }
  };

  // Generate hours and minutes for select options
  const hours = Array.from({ length: 12 }, (_, i) => {
    const hour = i + 1;
    return {
      value: hour.toString().padStart(2, "0"),
      label: hour.toString(),
    };
  });

  const minutes = Array.from({ length: 12 }, (_, i) => {
    const minute = i * 5;
    return {
      value: minute.toString().padStart(2, "0"),
      label: minute.toString().padStart(2, "0"),
    };
  });

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#EAF7F9] px-4 py-10">
      <div className="container mx-auto px-4 max-w-md md:max-w-xl">
        <div className="mb-6">
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="flex items-center gap-1 text-[#2E475D] hover:text-[#6A8E99] hover:bg-white/60"
          >
            <Link href="/">
              <ChevronLeft className="h-4 w-4" />
              Back to Home
            </Link>
          </Button>
        </div>

        {isSubmitted ? (
          <SuccessMessage message={responseMessage} />
        ) : (
          <div className="rounded-xl p-6 md:p-8">
            <div className="text-center mb-8">
              <h1 className="text-2xl font-semibold mb-2 text-[#2E475D]">Request a Demo</h1>
              <p className="text-gray-600">
                See how our platform can transform your healthcare experience
              </p>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                {/* Display form-level errors */}
                {form.formState.errors.root && (
                  <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-2 rounded-md text-sm">
                    {form.formState.errors.root.message}
                  </div>
                )}

                {/* Full Name */}
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2E475D]">Full Name</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter your name" {...field} className="bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Email Address */}
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2E475D]">Email Address</FormLabel>
                      <FormControl>
                        <Input placeholder="<EMAIL>" {...field} className="bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Role Selection */}
                <FormField
                  control={form.control}
                  name="role"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2E475D]">Role Selection</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="bg-white border-gray-300 text-gray-700">
                            <SelectValue placeholder="I'm a Provider" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent className="bg-white">
                          <SelectItem value="provider">
                            I&apos;m a Provider
                          </SelectItem>
                          <SelectItem value="patient">I&apos;m a Patient</SelectItem>
                          <SelectItem value="administrator">
                            I&apos;m an Administrator
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Phone Number */}
                <FormField
                  control={form.control}
                  name="phone"
                  render={() => (
                    <FormItem>
                      <FormLabel className="text-[#2E475D]">Phone Number</FormLabel>
                      <FormControl>
                        <Controller
                          name="phone"
                          control={form.control}
                          render={({ field: { value, onChange } }) => (
                            <PhoneInput
                              international
                              countryCallingCodeEditable={true}
                              defaultCountry="US"
                              value={value}
                              onChange={onChange}
                              inputComponent={CustomPhoneInput}
                            />
                          )}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Preferred Demo Type */}
                <FormField
                  control={form.control}
                  name="demoType"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel className="text-[#2E475D]">Preferred Demo Type</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="space-y-3"
                        >
                          <FormItem className="flex items-center space-x-2 space-y-0 rounded-md border p-3 bg-white">
                            <FormControl>
                              <RadioGroupItem value="live" id="live" />
                            </FormControl>
                            <FormLabel
                              className="font-normal cursor-pointer flex-1 text-gray-700"
                              htmlFor="live"
                            >
                              Live 1-on-1 Demo (Zoom/Google Meet)
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0 rounded-md border p-3 bg-white">
                            <FormControl>
                              <RadioGroupItem value="video" id="video" />
                            </FormControl>
                            <FormLabel
                              className="font-normal cursor-pointer flex-1 text-gray-700"
                              htmlFor="video"
                            >
                              Email Me a Demo Video
                            </FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-2 space-y-0 rounded-md border p-3 bg-white">
                            <FormControl>
                              <RadioGroupItem value="both" id="both" />
                            </FormControl>
                            <FormLabel
                              className="font-normal cursor-pointer flex-1 text-gray-700"
                              htmlFor="both"
                            >
                              I want both
                            </FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Preferred Date */}
                <FormField
                  control={form.control}
                  name="preferredDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel className="text-[#2E475D]">Preferred Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal bg-white border-gray-300",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>dd/mm/yyyy</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0 bg-white" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date(new Date().setHours(0, 0, 0, 0))
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Time */}
                <FormItem>
                  <FormLabel className="text-[#2E475D]">Time</FormLabel>
                  <div className="flex space-x-2">
                    <FormField
                      control={form.control}
                      name="preferredTime.hour"
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="w-[80px] bg-white border-gray-300 text-gray-700">
                              <SelectValue placeholder="Hour" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            {hours.map((hour) => (
                              <SelectItem key={hour.value} value={hour.value}>
                                {hour.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="preferredTime.minute"
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="w-[80px] bg-white border-gray-300 text-gray-700">
                              <SelectValue placeholder="Min" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            {minutes.map((minute) => (
                              <SelectItem
                                key={minute.value}
                                value={minute.value}
                              >
                                {minute.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="preferredTime.period"
                      render={({ field }) => (
                        <Select
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger className="w-[100px] bg-white border-gray-300 text-gray-700">
                              <SelectValue placeholder="AM/PM" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent className="bg-white">
                            <SelectItem value="AM">AM</SelectItem>
                            <SelectItem value="PM">PM</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    />
                  </div>
                  {(form.formState.errors.preferredTime?.hour ||
                    form.formState.errors.preferredTime?.minute ||
                    form.formState.errors.preferredTime?.period) && (
                    <p className="text-sm font-medium text-destructive mt-2">
                      Please select a valid time
                    </p>
                  )}
                </FormItem>

                {/* Additional Information */}
                <FormField
                  control={form.control}
                  name="additionalInfo"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-[#2E475D]">
                        Anything specific you&apos;d like us to cover?
                      </FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Your message here..."
                          className="min-h-[120px] resize-none bg-white border-gray-300 focus:border-[#6A8E99] focus:ring-[#6A8E99]"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Submit Button */}
                <Button
                  type="submit"
                  className="w-full bg-[#7da0a9] hover:bg-[#6b8f99] text-white mt-4"
                >
                  Request Demo
                </Button>

                <p className="text-center text-sm text-gray-600 mt-4">
                  We&apos;ll reach out within 24-48 hours with next steps.
                </p>
              </form>
            </Form>
          </div>
        )}
      </div>
    </div>
  );
}
