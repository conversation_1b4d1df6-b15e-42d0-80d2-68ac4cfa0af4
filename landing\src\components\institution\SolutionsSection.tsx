"use client";
// import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Building, Users2, Stethoscope, Heart } from "lucide-react";
import Link from "next/link";

const SolutionsSection = () => {
  const solutions = [
    {
      icon: <Building className="h-12 w-12 text-[#6A8E99]" />,
      title: "Large Hospital Systems",
      description: "Comprehensive AI solutions for multi-facility healthcare networks",
      features: [
        "Enterprise-wide patient data integration",
        "Advanced population health analytics",
        "Multi-department workflow optimization",
        "Centralized administrative dashboards"
      ],
      image: "/images/hospital_system.svg",
      cta: "Explore Enterprise Solutions"
    },
    {
      icon: <Users2 className="h-12 w-12 text-[#6A8E99]" />,
      title: "Community Clinics",
      description: "Scalable AI tools designed for smaller healthcare practices",
      features: [
        "Easy-to-use patient management",
        "Automated appointment scheduling",
        "Basic health analytics and reporting",
        "Affordable subscription plans"
      ],
      image: "/images/community_clinic.svg",
      cta: "Start Small Practice Plan"
    },
    {
      icon: <Stethoscope className="h-12 w-12 text-[#6A8E99]" />,
      title: "Specialty Practices",
      description: "Specialized AI modules for focused medical practices",
      features: [
        "Specialty-specific diagnostic tools",
        "Customizable treatment protocols",
        "Research and clinical trial support",
        "Specialized reporting capabilities"
      ],
      image: "/images/specialty_practice.svg",
      cta: "Customize for Your Specialty"
    },
    {
      icon: <Heart className="h-12 w-12 text-[#6A8E99]" />,
      title: "Long-term Care Facilities",
      description: "AI-powered solutions for extended care and monitoring",
      features: [
        "Continuous patient monitoring",
        "Medication management systems",
        "Family communication portals",
        "Compliance and documentation tools"
      ],
      image: "/images/longterm_care.svg",
      cta: "Improve Care Quality"
    }
  ];

  const integrations = [
    { name: "Epic", logo: "/images/epic_logo.svg" },
    { name: "Cerner", logo: "/images/cerner_logo.svg" },
    { name: "Allscripts", logo: "/images/allscripts_logo.svg" },
    { name: "athenahealth", logo: "/images/athena_logo.svg" },
    { name: "NextGen", logo: "/images/nextgen_logo.svg" },
    { name: "eClinicalWorks", logo: "/images/eclinical_logo.svg" }
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Solutions Grid */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Solutions for Every Healthcare Setting
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              From large hospital systems to small clinics, P.E.T.A.L.S. AI adapts to your 
              institution&apos;s unique needs and scale.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {solutions.map((solution, index) => (
              <Card key={index} className="bg-gray-50 border-0 shadow-none hover:shadow-lg transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-start gap-4 mb-6">
                    <div className="bg-[#EAF7F9] p-3 rounded-lg flex-shrink-0">
                      {solution.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-[#2E475D] mb-2">
                        {solution.title}
                      </h3>
                      <p className="text-gray-600">
                        {solution.description}
                      </p>
                    </div>
                  </div>

                  <div className="mb-6">
                    <ul className="space-y-2">
                      {solution.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-sm text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link href="/demo_request">
                    <Button 
                      variant="outline" 
                      className="w-full border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9]"
                    >
                      {solution.cta}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Integration Partners */}
        <div className="bg-[#F8FFFE] rounded-2xl p-8 sm:p-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Seamless EHR Integration
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              P.E.T.A.L.S. AI integrates with leading Electronic Health Record systems 
              to ensure smooth implementation and data continuity.
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 items-center">
            {integrations.map((integration, index) => (
              <div key={index} className="flex items-center justify-center">
                <div className="bg-white p-4 rounded-lg shadow-sm w-full h-16 flex items-center justify-center">
                  <span className="text-gray-600 font-medium text-sm">
                    {integration.name}
                  </span>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-sm text-gray-500 mb-4">
              Don&apos;t see your EHR system? We support custom integrations.
            </p>
            <Link href="/demo_request">
              <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9]">
                Discuss Custom Integration
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default SolutionsSection;
