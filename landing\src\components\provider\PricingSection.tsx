"use client";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Star, Users, Building2, Crown } from "lucide-react";
import Link from "next/link";

const PricingSection = () => {
  const plans = [
    {
      name: "Solo Practice",
      icon: <Users className="h-8 w-8 text-[#6A8E99]" />,
      description: "Perfect for individual practitioners and small clinics",
      price: "$49",
      period: "per provider/month",
      patientRange: "Up to 200 patients",
      features: [
        "AI clinical decision support",
        "Patient health monitoring",
        "Basic documentation tools",
        "Telehealth integration",
        "Mobile app access",
        "Email support",
        "HIPAA-compliant platform",
        "Basic reporting"
      ],
      cta: "Start Free Trial",
      popular: false,
      highlight: false
    },
    {
      name: "Group Practice",
      icon: <Building2 className="h-8 w-8 text-[#6A8E99]" />,
      description: "Ideal for multi-provider practices and clinics",
      price: "$99",
      period: "per provider/month",
      patientRange: "Up to 1,000 patients",
      features: [
        "Everything in Solo Practice",
        "Advanced AI insights",
        "Team collaboration tools",
        "Automated documentation",
        "Advanced scheduling",
        "Practice analytics",
        "Priority support",
        "EHR integrations",
        "Custom workflows",
        "Quality reporting"
      ],
      cta: "Start Free Trial",
      popular: true,
      highlight: true
    },
    {
      name: "Enterprise",
      icon: <Crown className="h-8 w-8 text-[#6A8E99]" />,
      description: "For healthcare systems and large organizations",
      price: "Custom",
      period: "pricing",
      patientRange: "Unlimited patients",
      features: [
        "Everything in Group Practice",
        "Multi-site management",
        "Population health analytics",
        "Advanced security features",
        "Custom integrations",
        "Dedicated account manager",
        "24/7 phone support",
        "Training and onboarding",
        "SLA guarantees",
        "White-label options"
      ],
      cta: "Contact Sales",
      popular: false,
      highlight: false
    }
  ];

  const additionalFeatures = [
    "HIPAA-compliant infrastructure",
    "99.9% uptime guarantee",
    "Data backup and recovery",
    "Regular security audits",
    "Ongoing platform updates",
    "Clinical support team"
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Choose Your Plan
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Flexible pricing options designed to grow with your practice. 
            All plans include our core AI features and HIPAA-compliant infrastructure.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto mb-16">
          {plans.map((plan, index) => (
            <Card 
              key={index} 
              className={`relative bg-white shadow-sm hover:shadow-lg transition-all duration-300 ${
                plan.highlight 
                  ? 'border-2 border-[#6A8E99] scale-105' 
                  : 'border border-gray-200'
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-[#6A8E99] text-white px-4 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                    <Star className="h-4 w-4" />
                    Most Popular
                  </div>
                </div>
              )}
              
              <CardContent className="p-8 flex flex-col h-full">
                {/* Header */}
                <div className="text-center mb-6">
                  <div className="mb-4 flex justify-center">
                    {plan.icon}
                  </div>
                  <h3 className="text-2xl font-bold text-[#2E475D] mb-2">
                    {plan.name}
                  </h3>
                  <p className="text-gray-600 text-sm mb-4">
                    {plan.description}
                  </p>
                  <div className="mb-2">
                    <span className="text-4xl font-bold text-[#2E475D]">
                      {plan.price}
                    </span>
                    <span className="text-gray-600 ml-1">
                      {plan.period}
                    </span>
                  </div>
                  <p className="text-sm text-gray-500">
                    {plan.patientRange}
                  </p>
                </div>

                {/* Features */}
                <div className="flex-grow mb-8">
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <Check className="h-5 w-5 text-[#6A8E99] flex-shrink-0 mt-0.5" />
                        <span className="text-gray-700 text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* CTA Button */}
                <div className="mt-auto">
                  {plan.name === "Enterprise" ? (
                    <Link href="/contact">
                      <Button 
                        className={`w-full py-3 rounded-md ${
                          plan.highlight
                            ? 'bg-[#6A8E99] hover:bg-[#597984] text-white'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                        }`}
                      >
                        {plan.cta}
                      </Button>
                    </Link>
                  ) : (
                    <Link href="/early_access">
                      <Button 
                        className={`w-full py-3 rounded-md ${
                          plan.highlight
                            ? 'bg-[#6A8E99] hover:bg-[#597984] text-white'
                            : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                        }`}
                      >
                        {plan.cta}
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Features */}
        <div className="bg-[#F2FFF6] rounded-2xl p-8 sm:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Included with Every Plan
            </h3>
            <p className="text-gray-600 max-w-2xl mx-auto">
              All plans come with enterprise-grade security, reliability, and support 
              to ensure your practice runs smoothly.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8">
            {additionalFeatures.map((feature, index) => (
              <div key={index} className="flex items-center gap-3">
                <Check className="h-5 w-5 text-[#6A8E99] flex-shrink-0" />
                <span className="text-gray-700">{feature}</span>
              </div>
            ))}
          </div>

          <div className="text-center">
            <p className="text-gray-600 mb-6">
              Questions about pricing or need a custom solution?
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo_request">
                <Button className="bg-[#6A8E99] hover:bg-[#597984] text-white px-8 py-3 rounded-md">
                  Schedule a Demo
                </Button>
              </Link>
              <Link href="/contact">
                <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] px-8 py-3 rounded-md">
                  Contact Sales
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
