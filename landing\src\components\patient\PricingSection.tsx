"use client";
import { Button } from "@/components/ui/button";
// import Link from "next/link";
import { useState } from "react";

const PricingSection = () => {
  const [isAnnual, setIsAnnual] = useState(false);

  const freePlanFeatures = [
    "Symptoms Contextualizer (basic use)",
    "Pain & Fatigue Tracker (daily check-ins)",
    "Access to blog"
  ];

  const premiumPlanFeatures = [
    "All Free Features",
    "Advanced AI insights",
    "Medication & Supplement Optimizer",
    "Personalized care recommendations",
    "Expert health summary for your doctor"
  ];

  const providerPlanFeatures = [
    "Dashboard to manage multiple patients",
    "Share insights & collaborate with patients",
    "Provider-grade AI tools",
    "Priority support",
    "Early access to new features"
  ];

  // Pricing data
  const premiumPricing = {
    monthly: { price: 10, period: "month" },
    annual: { price: 100, period: "year", savings: "20%" }
  };

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Choose Your Plan
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Start your health journey with the plan that fits your needs.
            Upgrade or downgrade anytime as your health goals evolve.
          </p>
        </div>

        <div className="text-center mb-12">

          {/* Billing Toggle */}
          <div className="flex items-center justify-center mb-8">
            <div className="bg-white rounded-lg p-1 shadow-sm border">
              <button
                onClick={() => setIsAnnual(false)}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
                  !isAnnual
                    ? 'bg-slate-700 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Monthly
              </button>
              <button
                onClick={() => setIsAnnual(true)}
                className={`px-6 py-2 rounded-md text-sm font-medium transition-colors relative ${
                  isAnnual
                    ? 'bg-slate-700 text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                Yearly
                <span className="absolute -top-2 -right-10 bg-orange-400 text-white text-xs px-2 py-1 rounded-full">
                  Save 20%
                </span>
              </button>
            </div>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">

          {/* Free Plan */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Free Plan</h3>
            </div>

            <div className="mb-6 flex-grow">
              {freePlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mt-auto">
              <Button
                variant="outline"
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white border-[#6A8E99] py-2 px-4 rounded-md"
              >
                Get Started Free
              </Button>
            </div>
          </div>

          {/* Premium Plan (Patients) */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Premium Plan (Patients)</h3>
            </div>

            <div className="mb-6 flex-grow">
              {premiumPlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mb-4">
              <div className="text-2xl font-bold text-[#2E475D] mb-1">
                ${isAnnual ? premiumPricing.annual.price : premiumPricing.monthly.price}/{isAnnual ? premiumPricing.annual.period : premiumPricing.monthly.period}
              </div>
              <p className="text-sm text-gray-600">Free 7-day trial available</p>
            </div>

            <div className="text-center mt-auto">
              <Button
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white py-2 px-4 rounded-md"
              >
                Start Free Trial
              </Button>
            </div>
          </div>

          {/* Provider (Custom) */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Provider (Custom)</h3>
            </div>

            <div className="mb-6 flex-grow">
              {providerPlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mb-4">
              <p className="text-sm text-gray-600 italic">Custom pricing (based on clinic size or users)</p>
            </div>

            <div className="text-center mt-auto">
              <Button
                variant="outline"
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white py-2 px-4 rounded-md"
              >
                Message Us
              </Button>
            </div>
          </div>

        </div>
      </div>
    </section>
  );
};

export default PricingSection;