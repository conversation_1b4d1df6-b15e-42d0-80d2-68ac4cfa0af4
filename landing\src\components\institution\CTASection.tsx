"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Phone, Mail, Calendar, MessageSquare, Clock, Shield } from "lucide-react";
import Link from "next/link";

const CTASection = () => {
  const contactOptions = [
    {
      icon: <Calendar className="h-6 w-6 text-[#6A8E99]" />,
      title: "Schedule a Demo",
      description: "See P.E.T.A.L.S. AI in action with a personalized demonstration",
      action: "Book Demo",
      href: "/demo_request"
    },
    {
      icon: <Phone className="h-6 w-6 text-[#6A8E99]" />,
      title: "Speak with Sales",
      description: "Discuss your institution's needs with our healthcare AI experts",
      action: "Call Sales",
      href: "tel:******-PETALS-AI"
    },
    {
      icon: <Mail className="h-6 w-6 text-[#6A8E99]" />,
      title: "Get Custom Quote",
      description: "Receive a tailored pricing proposal for your organization",
      action: "Request Quote",
      href: "/demo_request"
    },
    {
      icon: <MessageSquare className="h-6 w-6 text-[#6A8E99]" />,
      title: "Chat with Expert",
      description: "Get immediate answers to your questions from our team",
      action: "Start Chat",
      href: "#chat"
    }
  ];

  const guarantees = [
    {
      icon: <Clock className="h-8 w-8 text-[#6A8E99]" />,
      title: "30-Day Implementation",
      description: "Get up and running quickly with our proven deployment process"
    },
    {
      icon: <Shield className="h-8 w-8 text-[#6A8E99]" />,
      title: "100% HIPAA Compliant",
      description: "Enterprise-grade security and compliance built-in from day one"
    }
  ];

  return (
    <section className="py-16 bg-[#EAF7F9]">
      <div className="container mx-auto px-4">
        {/* Main CTA */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Ready to Transform Your Healthcare Institution?
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto mb-8">
            Join hundreds of healthcare institutions already using P.E.T.A.L.S. AI to improve 
            patient outcomes, reduce costs, and enhance care delivery. Get started today.
          </p>
          
          <div className="flex flex-col sm:flex-row justify-center gap-4 sm:gap-6 mb-12">
            <Link href="/demo_request">
              <Button className="w-full sm:w-auto bg-[#6A8E99] hover:bg-[#597984] text-white rounded-md py-3 px-8 min-w-[200px] text-lg">
                Schedule Your Demo
              </Button>
            </Link>
            <Link href="/early_access">
              <Button variant="outline" className="w-full sm:w-auto border-[#6A8E99] text-[#6A8E99] hover:bg-white py-3 px-8 min-w-[200px] text-lg">
                Start Free Trial
              </Button>
            </Link>
          </div>
        </div>

        {/* Contact Options */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-[#2E475D] text-center mb-8">
            Multiple Ways to Get Started
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 max-w-6xl mx-auto">
            {contactOptions.map((option, index) => (
              <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-6 text-center">
                  <div className="bg-[#EAF7F9] w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4">
                    {option.icon}
                  </div>
                  <h4 className="font-semibold text-[#2E475D] mb-2">
                    {option.title}
                  </h4>
                  <p className="text-sm text-gray-600 mb-4">
                    {option.description}
                  </p>
                  <Link href={option.href}>
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] w-full"
                    >
                      {option.action}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Guarantees */}
        <div className="bg-white rounded-2xl p-8 sm:p-12">
          <h3 className="text-2xl font-bold text-[#2E475D] text-center mb-8">
            Our Commitment to You
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {guarantees.map((guarantee, index) => (
              <div key={index} className="flex items-start gap-4">
                <div className="bg-[#EAF7F9] p-3 rounded-lg flex-shrink-0">
                  {guarantee.icon}
                </div>
                <div>
                  <h4 className="font-semibold text-[#2E475D] mb-2">
                    {guarantee.title}
                  </h4>
                  <p className="text-gray-600">
                    {guarantee.description}
                  </p>
                </div>
              </div>
            ))}
          </div>

          <div className="text-center mt-8 pt-8 border-t border-gray-100">
            <p className="text-sm text-gray-600 mb-4">
              Questions about implementation, pricing, or features?
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="tel:******-PETALS-AI" className="text-[#6A8E99] font-medium hover:underline">
                📞 234-700-PETALS-AI
              </a>
              <a href="mailto:<EMAIL>" className="text-[#6A8E99] font-medium hover:underline">
                ✉️ <EMAIL>
              </a>
            </div>
          </div>
        </div>

        {/* Final CTA */}
        <div className="text-center mt-12">
          <p className="text-gray-600 mb-4">
            Ready to see the difference P.E.T.A.L.S. AI can make for your institution?
          </p>
          <Link href="/demo_request">
            <Button className="bg-[#6A8E99] hover:bg-[#597984] text-white rounded-md py-3 px-8">
              Get Started Today
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
