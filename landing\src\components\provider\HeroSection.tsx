"use client";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";
import Link from "next/link";

const HeroSection = () => {
  const [isMobile, setIsMobile] = useState(false);
  // const [currentImageIndex, setCurrentImageIndex] = useState(0);
  // const [isTransitioning, setIsTransitioning] = useState(false);
  
  // Array of dashboard images to rotate through
  const dashboardImages = [
    "/images/provider_main.svg",
    "/images/provider_main.svg"
  ];

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    // Initial check
    checkIsMobile();
    
    // Add event listener
    window.addEventListener('resize', checkIsMobile);
    
    // Cleanup
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  // Effect for image rotation with fade transition
  // useEffect(() => {
  //   const intervalId = setInterval(() => {
  //     // Start transition
  //     setIsTransitioning(true);
      
  //     // Wait for fade out, then change image
  //     const timeoutId = setTimeout(() => {
  //       setCurrentImageIndex((prevIndex) => (prevIndex + 1) % dashboardImages.length);
  //       // Complete transition (fade in new image)
  //       setIsTransitioning(false);
  //     }, 500); // Half of the transition time
      
  //     return () => clearTimeout(timeoutId);
  //   }, 5000);
    
  //   // Cleanup interval on component unmount
  //   return () => clearInterval(intervalId);
  // }, []);

  const bgStyle = isMobile
    ? { backgroundImage: "url('/images/hero_bg.svg')", backgroundRepeat: "no-repeat", backgroundSize: "auto 80%", backgroundPosition: "top" }
    : { backgroundImage: "url('/images/hero_bg.svg')", backgroundRepeat: "no-repeat", backgroundSize: "100% auto", backgroundPosition: "top" };

  return (
    <section 
      className="pt-16 pb-10 sm:pt-20 md:pt-28 lg:pt-32 md:pb-20 bg-[#EAF7F9] relative"
      style={bgStyle}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col items-center text-center">
          <div className="flex items-center justify-center mb-4">
            <div className="bg-white/80 px-5 py-2 rounded-full shadow-sm">
              <span className="flex items-center text-[#6A8E99] font-medium">
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z" stroke="#6A8E99" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M9.09003 12V10.52C9.09003 8.60999 10.45 7.83999 12.10 8.78999L13.38 9.52999L14.66 10.27C16.31 11.22 16.31 12.78 14.66 13.73L13.38 14.47L12.10 15.21C10.45 16.16 9.09003 15.38 9.09003 13.48V12Z" stroke="#6A8E99" strokeWidth="1.5" strokeMiterlimit="10" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
                AI-Powered Care
              </span>
            </div>
          </div>
          
          <h2 className="text-[#2E475D] text-xl sm:text-2xl font-semibold mb-1">P.E.T.A.L.S. AI</h2>
          <p className="text-[#6A8E99] text-sm sm:text-base mb-6">
            Personal Empowerment Through Technology and Advanced Learning Systems
          </p>
          
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-[#2E475D] leading-tight max-w-4xl">
            Reimagine Practice. Serve More. <span className="text-[#6A8E99]">Stress Less</span>
          </h1>

          <p className="text-lg sm:text-xl text-gray-600 mt-6 max-w-3xl">
            Empower your practice with AI-driven insights, streamlined workflows, and enhanced patient care.
            Join thousands of healthcare providers transforming their practice with P.E.T.A.L.S. AI.
          </p>
          
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4 sm:gap-6">
            <Link href="/early_access">
              <Button className="w-full sm:w-auto bg-[#6A8E99] hover:bg-[#597984] text-white rounded-md py-2 px-8 min-w-[180px]">
                Join Waitlist
              </Button>
            </Link>
            <Link href="/demo_request">
              <Button variant="outline" className="w-full sm:w-auto border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] py-2 px-8 min-w-[180px]">
                Request a Demo
              </Button>
            </Link>
          </div>
        </div>
        
        <div className="mt-8 max-w-5xl mx-auto rounded-2xl">
          <div className="bg-inherit rounded-2xl overflow-hidden relative h-[250px] sm:h-[350px] md:h-[450px] lg:h-[600px]">
            {/* Render only the first image without transition */}
            <Image
              src={dashboardImages[0]}
              alt="Healthcare AI Platform Dashboard"
              width={1000}
              height={600}
              className="w-full h-full object-contain rounded-2xl"
              priority={true}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 