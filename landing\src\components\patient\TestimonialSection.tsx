"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { <PERSON><PERSON> } from "@/components/ui/button";

const testimonials = [
  {
    quote: "Petals Health has transformed how I manage my diabetes. The AI catches patterns I would have missed, and the virtual consultations save me so much time.",
    name: "<PERSON><PERSON> Jummy",
    title: "Financial Counselor",
    avatar: "/images/User_Thumb01.svg",
    initials: "<PERSON><PERSON>"
  },
  {
    quote: "I'm impressed by how Petals Health augments traditional care. The AI asks the right questions and the data tracking helps me better understand my patients' experiences between visits.",
    name: "<PERSON>",
    title: "Math Teacher",
    avatar: "/images/User_Thumb02.svg",
    initials: "RE"
  },
  {
    quote: "As someone who travels constantly, having access to healthcare guidance anywhere is invaluable. The AI gives me peace of mind when I'm far from my regular doctor.",
    name: "<PERSON><PERSON>",
    title: "Psychology Student",
    avatar: "/images/User_Thumb03.svg",
    initials: "<PERSON><PERSON>"
  }
];

const TestimonialSection = () => {
  const [currentPage, setCurrentPage] = useState(0);
  const itemsPerPage = 1;
  const totalPages = Math.ceil(testimonials.length / itemsPerPage);

  const displayedTestimonials = testimonials;

  const goToPrevious = () => {
    setCurrentPage((prev) => (prev > 0 ? prev - 1 : totalPages - 1));
  };

  const goToNext = () => {
    setCurrentPage((prev) => (prev < totalPages - 1 ? prev + 1 : 0));
  };

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-[#F8FFFE]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            What Our Users Say
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Hear from real users who have transformed their health journey with P.E.T.A.L.S. AI.
          </p>
        </div>

        {/* Desktop View */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {displayedTestimonials.map((testimonial, index) => (
            <Card
              key={index}
              className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300 h-full flex flex-col"
            >
              <CardContent className="p-6 flex flex-col flex-grow">
                <blockquote className="mb-6 text-gray-700 flex-grow">
                  &quot;{testimonial.quote}&quot;
                </blockquote>
                <div className="flex items-center">
                  <Avatar className="h-12 w-12 mr-4">
                    <AvatarImage src={testimonial.avatar} alt={testimonial.name} />
                    <AvatarFallback>{testimonial.initials}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold text-[#2E475D]">{testimonial.name}</p>
                    <p className="text-sm text-gray-500">{testimonial.title}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Mobile View */}
        <div className="md:hidden">
          <Card className="bg-white border border-gray-100 shadow-sm">
            <CardContent className="p-6">
              <blockquote className="mb-6 text-gray-700">
                &quot;{testimonials[currentPage].quote}&quot;
              </blockquote>
              <div className="flex items-center">
                <Avatar className="h-12 w-12 mr-4">
                  <AvatarImage 
                    src={testimonials[currentPage].avatar} 
                    alt={testimonials[currentPage].name} 
                  />
                  <AvatarFallback>{testimonials[currentPage].initials}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold text-[#2E475D]">{testimonials[currentPage].name}</p>
                  <p className="text-sm text-gray-500">{testimonials[currentPage].title}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="flex justify-center mt-6 space-x-4">
            <Button 
              variant="outline" 
              className="rounded-full h-10 w-10 p-0 flex items-center justify-center border-gray-300"
              onClick={goToPrevious}
            >
              <span className="sr-only">Previous</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </Button>
            <Button 
              variant="outline" 
              className="rounded-full h-10 w-10 p-0 flex items-center justify-center border-gray-300"
              onClick={goToNext}
            >
              <span className="sr-only">Next</span>
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M9 18l6-6-6-6" />
              </svg>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialSection;
