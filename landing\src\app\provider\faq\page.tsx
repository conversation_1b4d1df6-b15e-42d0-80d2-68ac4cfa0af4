"use client";

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Footer from "@/components/Footer"; // Import the Footer component

interface FAQItem {
  question: string;
  answer: string;
}

const providerFaqs: FAQItem[] = [
  {
    question: "How does Petals AI benefit healthcare providers?",
    answer: "Petals AI offers tools that streamline clinical workflows, provide AI-powered diagnostic assistance, automate administrative tasks, and enhance patient engagement, allowing providers to focus more on patient care."
  },
  {
    question: "Can Petals AI integrate with existing EHR systems?",
    answer: "Yes, Petals AI is designed to be compatible with various Electronic Health Record (EHR) systems to ensure a seamless integration process. We provide technical support to facilitate this."
  },
  {
    question: "What kind of AI capabilities does Petals AI offer for diagnostics?",
    answer: "Petals AI utilizes advanced machine learning algorithms to analyze medical images, lab results, and patient data, providing rapid and accurate diagnostic insights to support clinical decision-making."
  },
  {
    question: "Is patient data privacy maintained with Petals AI?",
    answer: "Absolutely. We are fully compliant with HIPAA and other stringent data privacy regulations. Patient data is encrypted, anonymized where appropriate, and accessed only under strict protocols."
  },
  {
    question: "How can Petals AI help with administrative burdens?",
    answer: "Petals AI automates routine administrative tasks such as scheduling, billing, patient intake forms, and prescription refills, significantly reducing the workload on your staff and improving efficiency."
  },
  {
    question: "What support is available for providers using Petals AI?",
    answer: "We offer dedicated technical support, comprehensive training modules, and a knowledge base to help providers maximize the benefits of Petals AI. Our team is always ready to assist."
  }
];

export default function ProviderFAQPage() {
  return (
    <>
      <div className="min-h-screen bg-[#EAF7F9] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-4xl font-bold text-center text-[#2E475D] mb-10">
            Provider Frequently Asked Questions
          </h1>
          <div className="space-y-6">
            {providerFaqs.map((faq, index) => (
              <Accordion key={index} type="single" collapsible className="w-full">
                <AccordionItem value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-lg font-semibold text-[#2E475D] hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-700 leading-relaxed px-4 py-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}