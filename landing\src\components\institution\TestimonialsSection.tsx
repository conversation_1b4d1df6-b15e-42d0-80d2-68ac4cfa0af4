"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote } from "lucide-react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      quote: "P.E.T.A.L.S. AI has transformed how we deliver care across our hospital network. The predictive analytics have helped us reduce readmissions by 35% while improving patient satisfaction scores significantly.",
      author: "Dr. <PERSON>",
      title: "Chief Medical Officer",
      institution: "Metropolitan Health System",
      institutionType: "Large Hospital Network",
      rating: 5,
      metrics: ["35% reduction in readmissions", "25% improvement in patient satisfaction", "$2.5M annual savings"]
    },
    {
      quote: "The AI-powered clinical decision support has been invaluable for our emergency department. We're making faster, more accurate diagnoses, and our staff feels more confident in their treatment decisions.",
      author: "Dr. <PERSON>",
      title: "Emergency Department Director",
      institution: "Regional Medical Center",
      institutionType: "500-bed Hospital",
      rating: 5,
      metrics: ["40% faster diagnosis times", "20% reduction in medical errors", "90% staff satisfaction"]
    },
    {
      quote: "As a small practice, we needed an AI solution that was both powerful and easy to use. P.E.T.A.L.S. AI delivered exactly that, helping us provide better care while reducing our administrative burden.",
      author: "Dr. <PERSON>",
      title: "Practice Owner",
      institution: "Park Family Medicine",
      institutionType: "Community Clinic",
      rating: 5,
      metrics: ["50% reduction in documentation time", "30% increase in patient throughput", "95% patient satisfaction"]
    },
    {
      quote: "The population health insights have been game-changing for our preventive care programs. We can now identify at-risk patients early and intervene before complications arise.",
      author: "Dr. Robert Thompson",
      title: "Chief Population Health Officer",
      institution: "Community Health Partners",
      institutionType: "Multi-Clinic System",
      rating: 5,
      metrics: ["60% improvement in preventive care", "45% reduction in complications", "25% cost savings"]
    }
  ];

  const awards = [
    {
      title: "Healthcare Innovation Award 2024",
      organization: "American Hospital Association",
      description: "Recognized for outstanding innovation in AI-powered healthcare solutions"
    },
    {
      title: "Best Healthcare AI Platform",
      organization: "Healthcare Technology Report",
      description: "Top-rated AI platform for healthcare institutions"
    },
    {
      title: "HIMSS Innovation Award",
      organization: "Healthcare Information Management",
      description: "Excellence in healthcare information technology innovation"
    }
  ];

  return (
    <section className="py-16 bg-[#F8FFFE]">
      <div className="container mx-auto px-4">
        {/* Testimonials */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Trusted by Healthcare Leaders
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Healthcare institutions across the country are seeing remarkable results 
              with P.E.T.A.L.S. AI. Here&apos;s what they have to say.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-8">
                  <div className="flex items-start gap-4 mb-6">
                    <Quote className="h-8 w-8 text-[#6A8E99] flex-shrink-0 mt-1" />
                    <div>
                      <div className="flex items-center gap-1 mb-3">
                        {[...Array(testimonial.rating)].map((_, i) => (
                          <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        ))}
                      </div>
                      <p className="text-gray-700 italic mb-6 leading-relaxed">
                        &quot;{testimonial.quote}&quot;
                      </p>
                    </div>
                  </div>

                  <div className="border-t pt-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-[#2E475D]">
                          {testimonial.author}
                        </h4>
                        <p className="text-sm text-gray-600">
                          {testimonial.title}
                        </p>
                        <p className="text-sm font-medium text-[#6A8E99]">
                          {testimonial.institution}
                        </p>
                        <p className="text-xs text-gray-500">
                          {testimonial.institutionType}
                        </p>
                      </div>
                    </div>

                    <div className="bg-[#F0F9FF] rounded-lg p-4">
                      <h5 className="text-sm font-semibold text-gray-800 mb-2">Key Results:</h5>
                      <div className="grid grid-cols-1 gap-1">
                        {testimonial.metrics.map((metric, metricIndex) => (
                          <div key={metricIndex} className="flex items-center gap-2">
                            <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full flex-shrink-0"></div>
                            <span className="text-xs text-gray-600">{metric}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Awards and Recognition */}
        <div className="bg-white rounded-2xl p-8 sm:p-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Industry Recognition
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              P.E.T.A.L.S. AI has been recognized by leading healthcare organizations 
              for innovation and excellence in AI-powered healthcare solutions.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {awards.map((award, index) => (
              <div key={index} className="text-center">
                <div className="bg-[#EAF7F9] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Star className="h-8 w-8 text-[#6A8E99]" />
                </div>
                <h3 className="font-semibold text-[#2E475D] mb-2">
                  {award.title}
                </h3>
                <p className="text-sm text-[#6A8E99] font-medium mb-2">
                  {award.organization}
                </p>
                <p className="text-xs text-gray-600">
                  {award.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
