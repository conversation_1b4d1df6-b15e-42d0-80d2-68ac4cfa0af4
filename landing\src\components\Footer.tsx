"use client";
import Image from "next/image";
import Link from "next/link";

type FooterLink = {
  name: string;
  href: string;
  hasArrow?: boolean;
};

type FooterLinksMap = {
  [category: string]: FooterLink[];
};

const Footer = () => {
  const footerLinks: FooterLinksMap = {
    'Quick Link': [
      { name: 'About us', href: '#about-us' },
      { name: 'Who its for', href: '#who-its-for' },
      { name: 'Features', href: '#features' },
      { name: 'Join Waitlist', href: '/early_access' },
      { name: 'Privacy policy', href: '#privacy-policy' },
    ],
    'Support': [
      { name: 'Contact Us', href: '#contact-us' },
      { name: 'Developers', href: '#developers' },
      { name: 'Documentation', href: '#documentation' },
      { name: 'Integrations', href: '#integrations' },
    ],
    'Company': [
      { name: 'About', href: '#about' },
      { name: 'Press', href: '#press' },
      { name: 'Events', href: '#events' },
      { name: 'Request Demo', href: '/demo_request', hasArrow: true },
    ],
  };

  return (
    <footer className="bg-[#DBFFDF] text-gray-700 py-12">
      <div className="container mx-auto px-4 md:px-8">
        {/* Desktop Layout */}
        <div className="hidden md:flex justify-between">
          {/* Logo Section */}
          <div className="flex items-start">
            <Link href="/" className="flex items-center">
              <Image
                src="/images/logo_alone.svg"
                alt="Petals Ai Logo"
                width={48}
                height={48}
                className="w-12 h-12"
              />
              <span className="ml-2 text-2xl font-bold text-[#2E475D]">Petals Ai</span>
            </Link>
          </div>

          {/* Links in three columns */}
          {Object.keys(footerLinks).map((category) => (
            <div key={category} className="flex flex-col">
              <h3 className="text-base font-semibold mb-4">{category}</h3>
              <ul className="space-y-3">
                {footerLinks[category].map((link) => (
                  <li key={link.name}>
                    <Link 
                      href={link.href} 
                      className={`hover:underline flex items-center ${link.name === 'Request Demo' ? 'font-medium' : ''}`}
                    >
                      {link.name}
                      {link.name === 'Request Demo' && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Mobile Layout */}
        <div className="md:hidden">
          {/* Logo Section */}
          <div className="flex items-center justify-center mb-8">
            <Link href="/" className="flex items-center">
              <Image
                src="/images/logo_alone.svg"
                alt="Petals Ai Logo"
                width={40}
                height={40}
                className="w-10 h-10"
              />
              <span className="ml-2 text-xl font-bold text-[#2E475D]">Petals Ai</span>
            </Link>
          </div>

          {/* Links Sections */}
          <div className="grid grid-cols-1 gap-8">
            {Object.keys(footerLinks).map((category) => (
              <div key={category}>
                <h3 className="text-base font-semibold mb-3">{category}</h3>
                <ul className="space-y-2">
                  {footerLinks[category].map((link) => (
                    <li key={link.name}>
                      <Link 
                        href={link.href} 
                        className={`hover:underline flex items-center ${link.name === 'Request Demo' ? 'font-medium' : ''}`}
                      >
                        {link.name}
                        {link.name === 'Request Demo' && (
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                          </svg>
                        )}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Divider Line */}
        <div className="border-t border-gray-200 my-8"></div>

        {/* Copyright */}
        <div className="text-sm text-gray-700">
          <p>@ 2025. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 