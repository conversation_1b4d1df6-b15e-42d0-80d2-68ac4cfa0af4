"use client";
import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote } from "lucide-react";

const TestimonialsSection = () => {
  const testimonials = [
    {
      quote: "P.E.T.A.L.S. AI has revolutionized how I manage my patients with chronic conditions. The AI insights help me make more informed decisions, and my patients love the personalized care recommendations.",
      author: "Dr. <PERSON>",
      title: "Internal Medicine Physician",
      practice: "Mitchell Family Medicine",
      location: "Austin, TX",
      rating: 5,
      metrics: ["40% improvement in patient engagement", "25% reduction in appointment time", "95% patient satisfaction"]
    },
    {
      quote: "The integration with our EHR was seamless, and the clinical decision support has been invaluable. Our team is more efficient, and we're seeing better patient outcomes across the board.",
      author: "Dr. <PERSON>",
      title: "Cardiologist",
      practice: "Heart Health Associates",
      location: "San Francisco, CA",
      rating: 5,
      metrics: ["30% faster diagnosis", "20% reduction in readmissions", "Enhanced care coordination"]
    },
    {
      quote: "As a solo practitioner, P.E.T.A.L.S. AI gives me the tools I need to compete with larger practices. The automated documentation alone saves me hours each week.",
      author: "Dr. <PERSON>",
      title: "Family Medicine",
      practice: "Rodriguez Family Care",
      location: "Phoenix, AZ",
      rating: 5,
      metrics: ["3 hours saved daily", "Improved work-life balance", "Better patient relationships"]
    },
    {
      quote: "The patient monitoring capabilities are exceptional. I can track my patients' progress remotely and intervene early when needed. It's transformed how I practice orthopedics.",
      author: "Dr. James Thompson",
      title: "Orthopedic Surgeon",
      practice: "Thompson Orthopedics",
      location: "Denver, CO",
      rating: 5,
      metrics: ["50% faster recovery tracking", "Reduced complications", "Improved patient compliance"]
    },
    {
      quote: "Our pediatric practice has seen remarkable improvements in family engagement. Parents love the insights about their children's health, and we can provide more personalized care.",
      author: "Dr. Lisa Park",
      title: "Pediatrician",
      practice: "Children's Health Center",
      location: "Seattle, WA",
      rating: 5,
      metrics: ["Enhanced family communication", "Better preventive care", "Increased vaccination rates"]
    },
    {
      quote: "The AI-powered clinical support has made me a better physician. I feel more confident in my diagnoses and treatment plans, knowing I have evidence-based recommendations at my fingertips.",
      author: "Dr. Robert Williams",
      title: "Emergency Medicine",
      practice: "Metro Emergency Group",
      location: "Chicago, IL",
      rating: 5,
      metrics: ["Faster decision making", "Reduced diagnostic errors", "Improved patient safety"]
    }
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-[#F8FFFE]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Trusted by Healthcare Providers Nationwide
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            See how P.E.T.A.L.S. AI is helping healthcare providers deliver better care, 
            improve efficiency, and enhance patient outcomes across different specialties.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {testimonials.map((testimonial, index) => (
            <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300 h-full">
              <CardContent className="p-6 flex flex-col h-full">
                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                  ))}
                </div>

                {/* Quote */}
                <div className="mb-6 flex-grow">
                  <Quote className="h-8 w-8 text-[#6A8E99] mb-3 opacity-50" />
                  <p className="text-gray-700 leading-relaxed italic">
                    &quot;{testimonial.quote}&quot;
                  </p>
                </div>

                {/* Author Info */}
                <div className="border-t border-gray-100 pt-4">
                  <div className="mb-3">
                    <h4 className="font-semibold text-[#2E475D]">{testimonial.author}</h4>
                    <p className="text-sm text-gray-600">{testimonial.title}</p>
                    <p className="text-sm text-gray-500">{testimonial.practice}</p>
                    <p className="text-sm text-gray-500">{testimonial.location}</p>
                  </div>

                  {/* Metrics */}
                  <div className="space-y-1">
                    {testimonial.metrics.map((metric, metricIndex) => (
                      <div key={metricIndex} className="flex items-center gap-2">
                        <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full flex-shrink-0"></div>
                        <span className="text-xs text-gray-600">{metric}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 bg-white rounded-2xl p-8 sm:p-12 shadow-sm border border-gray-100">
          <div className="text-center mb-8">
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Proven Results Across Healthcare
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">10,000+</div>
              <div className="text-gray-600">Healthcare Providers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">98%</div>
              <div className="text-gray-600">Provider Satisfaction</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">35%</div>
              <div className="text-gray-600">Average Time Savings</div>
            </div>
            <div className="text-center">
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">92%</div>
              <div className="text-gray-600">Would Recommend</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
