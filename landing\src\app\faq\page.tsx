"use client";

import React from 'react';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Footer from "@/components/Footer"; // Import the Footer component

interface FAQItem {
  question: string;
  answer: string;
}

const generalFaqs: FAQItem[] = [
  {
    question: "What is Petals AI?",
    answer: "Petals AI is a cutting-edge platform designed to assist healthcare providers, institutions, and patients with advanced AI tools, streamlining operations, improving diagnostics, and enhancing patient care."
  },
  {
    question: "How can Petals AI help me?",
    answer: "Petals AI offers various solutions including AI-powered diagnostic support, administrative automation, secure patient data management, and personalized health insights, tailored to different user needs."
  },
  {
    question: "Is my data secure with Petals AI?",
    answer: "Yes, data security and privacy are our top priorities. Petals AI employs robust encryption, compliance with HIPAA and other relevant regulations, and strict access controls to protect all sensitive information."
  },
  {
    question: "How do I get started with Petals AI?",
    answer: "You can join our early access list by selecting your role (patient, provider, or institution) on our Early Access page. We will notify you with instructions on how to get started once access is available."
  },
  {
    question: "What kind of support does Petals AI offer?",
    answer: "We offer comprehensive support including an extensive knowledge base, email support, and dedicated account managers for institutional clients. Our goal is to ensure a smooth and effective experience for all users."
  }
];

export default function GeneralFAQPage() {
  return (
    <>
      <div className="min-h-screen bg-[#EAF7F9] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-4xl font-bold text-center text-[#2E475D] mb-10">
            General Frequently Asked Questions
          </h1>
          <div className="space-y-6">
            {generalFaqs.map((faq, index) => (
              <Accordion key={index} type="single" collapsible className="w-full">
                <AccordionItem value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-lg font-semibold text-[#2E475D] hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-700 leading-relaxed px-4 py-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}