"use client";
import { Card, CardContent } from "@/components/ui/card";
import { 
  Stethoscope, 
  Brain, 
  Calendar, 
  FileText, 
  BarChart3, 
  Shield, 
  Users, 
  MessageSquare,
  Smartphone,
  Database
} from "lucide-react";

const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <Brain className="h-12 w-12 text-[#6A8E99]" />,
      title: "AI Clinical Assistant",
      description: "Get intelligent recommendations and evidence-based insights to support your clinical decision-making process."
    },
    {
      icon: <Stethoscope className="h-12 w-12 text-[#6A8E99]" />,
      title: "Patient Health Monitoring",
      description: "Real-time tracking of patient symptoms, medication adherence, and health metrics with AI-powered analysis."
    },
    {
      icon: <Calendar className="h-12 w-12 text-[#6A8E99]" />,
      title: "Smart Scheduling",
      description: "Automated appointment scheduling with AI optimization for better patient flow and reduced wait times."
    },
    {
      icon: <FileText className="h-12 w-12 text-[#6A8E99]" />,
      title: "Automated Documentation",
      description: "AI-powered clinical note generation and documentation to reduce administrative burden and improve accuracy."
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-[#6A8E99]" />,
      title: "Advanced Analytics",
      description: "Comprehensive reporting and analytics to track patient outcomes, practice performance, and quality metrics."
    },
    {
      icon: <Shield className="h-12 w-12 text-[#6A8E99]" />,
      title: "HIPAA Compliance",
      description: "Enterprise-grade security and full HIPAA compliance to protect patient data and maintain privacy."
    },
    {
      icon: <Users className="h-12 w-12 text-[#6A8E99]" />,
      title: "Team Collaboration",
      description: "Seamless communication and collaboration tools for healthcare teams and multidisciplinary care."
    },
    {
      icon: <MessageSquare className="h-12 w-12 text-[#6A8E99]" />,
      title: "Patient Communication",
      description: "Secure messaging, telehealth integration, and automated patient engagement tools."
    },
    {
      icon: <Smartphone className="h-12 w-12 text-[#6A8E99]" />,
      title: "Mobile Access",
      description: "Full-featured mobile app for providers to access patient data and manage care on the go."
    },
    {
      icon: <Database className="h-12 w-12 text-[#6A8E99]" />,
      title: "EHR Integration",
      description: "Seamless integration with major EHR systems to maintain workflow continuity and data consistency."
    }
  ];

  return (
    <section id="features" className="py-16 sm:py-20 md:py-24 bg-[#F8FFFE]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Comprehensive Provider Tools
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Everything you need to deliver exceptional patient care, streamline operations, 
            and improve clinical outcomes in one integrated platform.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
          {features.map((feature, index) => (
            <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1">
              <CardContent className="p-6 text-center">
                <div className="mb-4 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold text-[#2E475D] mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Feature Highlight */}
        <div className="mt-16 bg-white rounded-2xl p-8 sm:p-12 shadow-sm border border-gray-100">
          <div className="text-center max-w-4xl mx-auto">
            <h3 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Built for Modern Healthcare
            </h3>
            <p className="text-lg text-gray-600 mb-6">
              P.E.T.A.L.S. AI combines cutting-edge artificial intelligence with practical healthcare tools 
              to create a platform that truly understands the needs of healthcare providers.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-[#6A8E99] mb-2">99.9%</div>
                <div className="text-gray-600">Uptime Guarantee</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#6A8E99] mb-2">24/7</div>
                <div className="text-gray-600">Clinical Support</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-[#6A8E99] mb-2">SOC 2</div>
                <div className="text-gray-600">Type II Certified</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default KeyFeaturesSection;
