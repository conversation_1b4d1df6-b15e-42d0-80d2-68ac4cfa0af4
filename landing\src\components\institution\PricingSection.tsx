"use client";
import { <PERSON>, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Check, Star, Building2, Users, Zap } from "lucide-react";
import Link from "next/link";

const PricingSection = () => {
  const plans = [
    {
      name: "Clinic",
      icon: <Users className="h-8 w-8 text-[#6A8E99]" />,
      description: "Perfect for small practices and community clinics",
      price: "$299",
      period: "per month",
      patientRange: "Up to 500 patients",
      features: [
        "Basic AI health insights",
        "Patient management system",
        "Appointment scheduling",
        "Basic reporting and analytics",
        "Email support",
        "HIPAA-compliant infrastructure",
        "Mobile app access",
        "Standard integrations"
      ],
      cta: "Start Free Trial",
      popular: false
    },
    {
      name: "Hospital",
      icon: <Building2 className="h-8 w-8 text-[#6A8E99]" />,
      description: "Comprehensive solution for hospitals and health systems",
      price: "$1,299",
      period: "per month",
      patientRange: "Up to 5,000 patients",
      features: [
        "Advanced AI clinical decision support",
        "Population health analytics",
        "Multi-department workflows",
        "Advanced reporting and dashboards",
        "Priority phone and email support",
        "Enterprise security features",
        "Custom integrations",
        "Staff training and onboarding",
        "Predictive analytics",
        "Quality metrics tracking"
      ],
      cta: "Schedule Demo",
      popular: true
    },
    {
      name: "Enterprise",
      icon: <Zap className="h-8 w-8 text-[#6A8E99]" />,
      description: "Scalable solution for large healthcare networks",
      price: "Custom",
      period: "pricing",
      patientRange: "Unlimited patients",
      features: [
        "Full AI suite with custom models",
        "Multi-facility management",
        "Advanced population health insights",
        "Custom workflow automation",
        "Dedicated account manager",
        "24/7 priority support",
        "Custom API development",
        "Advanced security and compliance",
        "Research and analytics tools",
        "White-label options",
        "On-premise deployment available"
      ],
      cta: "Contact Sales",
      popular: false
    }
  ];

  const addOns = [
    {
      name: "Advanced Analytics Package",
      description: "Enhanced reporting and predictive analytics",
      price: "$199/month"
    },
    {
      name: "Telehealth Integration",
      description: "Seamless video consultation platform",
      price: "$149/month"
    },
    {
      name: "Research Module",
      description: "Clinical research and trial management tools",
      price: "$299/month"
    },
    {
      name: "Additional User Licenses",
      description: "Extra staff access beyond plan limits",
      price: "$25/user/month"
    }
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Pricing Plans */}
        <div className="mb-16">
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Flexible Pricing for Every Institution
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose the plan that fits your institution&apos;s size and needs. 
              All plans include core AI features and can be customized as you grow.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {plans.map((plan, index) => (
              <Card
                key={index}
                className={`relative shadow-sm hover:shadow-lg transition-all duration-300 ${
                  plan.popular ? 'border-2 border-[#6A8E99] bg-white scale-105' : 'border border-gray-200 bg-white'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <div className="bg-[#6A8E99] text-white px-4 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      Most Popular
                    </div>
                  </div>
                )}
                
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <div className="bg-[#EAF7F9] w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                      {plan.icon}
                    </div>
                    <h3 className="text-2xl font-bold text-[#2E475D] mb-2">
                      {plan.name}
                    </h3>
                    <p className="text-gray-600 text-sm mb-4">
                      {plan.description}
                    </p>
                    <div className="mb-2">
                      <span className="text-3xl font-bold text-[#2E475D]">
                        {plan.price}
                      </span>
                      <span className="text-gray-600 ml-1">
                        {plan.period}
                      </span>
                    </div>
                    <p className="text-sm text-[#6A8E99] font-medium">
                      {plan.patientRange}
                    </p>
                  </div>

                  <div className="mb-8">
                    <ul className="space-y-3">
                      {plan.features.map((feature, featureIndex) => (
                        <li key={featureIndex} className="flex items-start gap-3">
                          <Check className="h-4 w-4 text-[#6A8E99] flex-shrink-0 mt-0.5" />
                          <span className="text-sm text-gray-600">{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Link href="/demo_request" className="block">
                    <Button 
                      className={`w-full ${
                        plan.popular 
                          ? 'bg-[#6A8E99] hover:bg-[#597984] text-white' 
                          : 'bg-white border border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9]'
                      }`}
                      variant={plan.popular ? "default" : "outline"}
                    >
                      {plan.cta}
                    </Button>
                  </Link>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Add-ons */}
        <div className="bg-[#F8FFFE] rounded-2xl p-8 sm:p-12">
          <div className="text-center mb-8">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-4">
              Optional Add-ons
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Enhance your P.E.T.A.L.S. AI experience with specialized modules 
              designed for specific healthcare needs.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {addOns.map((addon, index) => (
              <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-[#2E475D] mb-2">
                        {addon.name}
                      </h3>
                      <p className="text-sm text-gray-600 mb-3">
                        {addon.description}
                      </p>
                    </div>
                    <div className="text-right ml-4">
                      <span className="font-semibold text-[#6A8E99]">
                        {addon.price}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-sm text-gray-600 mb-4">
              Need a custom solution? We offer tailored packages for unique requirements.
            </p>
            <Link href="/demo_request">
              <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9]">
                Discuss Custom Pricing
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
