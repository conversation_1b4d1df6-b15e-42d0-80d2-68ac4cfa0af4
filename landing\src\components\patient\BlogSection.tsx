"use client";

import Image from "next/image";
import Link from "next/link";

export default function BlogSection() {
    const blogPosts = [
        {
            id: 1,
            image: "/images/blog1.svg",
            title: "Chronic illness Warrior",
            description: "People managing diabetes, hypertension, autoimmune conditions, or other chronic health challenges.",
            readMore: "Read more"
        },
        {
            id: 2,
            image: "/images/blog2.svg",
            title: "Medication Managers",
            description: "Users with complex medication regimens or those tracking symptoms to optimize their health.",
            readMore: "Read more"
        },
        {
            id: 3,
            image: "/images/blog3.svg",
            title: "Health Researchers",
            description: "Anyone tired of Googling symptoms and wanting reliable, personalized health information.",
            readMore: "Read more"
        },
        {
            id: 4,
            image: "/images/blog4.svg",
            title: "Busy Professionals",
            description: "People needing expert healthcare guidance that fits their demanding schedule.",
            readMore: "Read more"
        },
        {
            id: 5,
            image: "/images/blog5.svg",
            title: "Busy Professionals",
            description: "People needing expert healthcare guidance that fits their demanding schedule.",
            readMore: "Read more"
        },
        {
            id: 6,
            image: "/images/blog6.svg",
            title: "Busy Professionals",
            description: "People needing expert healthcare guidance that fits their demanding schedule.",
            readMore: "Read more"
        }
    ];

    return (
        <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
            <div className="container mx-auto px-4 sm:px-6 lg:px-28">
                <div className="flex justify-between items-center mb-8">
                    <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900">Blogs</h2>
                    <Link href="/blog" className="text-blue-500 hover:text-blue-600 transition-colors">
                        View all
                    </Link>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {blogPosts.map((post) => (
                        <div key={post.id} className="bg-white rounded-lg overflow-hidden shadow-sm border border-gray-100 flex flex-col">
                            <div className="aspect-[4/3] bg-gray-100 overflow-hidden">
                                <Image
                                    width={10}
                                    height={10}
                                    src={post.image}
                                    alt={post.title}
                                    className="w-full h-full object-cover"
                                />
                            </div>
                            <div className="p-5 flex flex-col flex-grow">
                                <h3 className="text-lg font-semibold text-gray-900 mb-2">{post.title}</h3>
                                <p className="text-gray-600 text-sm mb-4 leading-relaxed flex-grow">{post.description}</p>
                                <a href="#" className="text-[#2A9D8F] hover:text-[#238276] text-sm font-medium transition-colors">
                                    {post.readMore}
                                </a>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </section>
    );
}