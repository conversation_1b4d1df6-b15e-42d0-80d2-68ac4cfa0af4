import { NextResponse } from 'next/server';
import { supabase } from '@/config/env';

export async function POST(request: Request) {
  try {
    const data = await request.json();
    
    // Check if email already exists
    const { data: existingEmailData, error: emailError } = await supabase
      .from('demo_requests')
      .select('id')
      .eq('email', data.email)
      .limit(1);
    
    if (emailError) {
      console.error('Error checking existing email:', emailError);
      return NextResponse.json(
        { error: 'Failed to process demo request' },
        { status: 500 }
      );
    }
    
    if (existingEmailData && existingEmailData.length > 0) {
      return NextResponse.json(
        { message: 'You have already submitted a demo request with this email.' },
        { status: 200 }
      );
    }
    
    // Check if phone number already exists (if provided)
    if (data.phone) {
      const { data: existingPhoneData, error: phoneError } = await supabase
        .from('demo_requests')
        .select('id')
        .eq('phone', data.phone)
        .limit(1);
      
      if (phoneError) {
        console.error('Error checking existing phone number:', phoneError);
        return NextResponse.json(
          { error: 'Failed to process demo request' },
          { status: 500 }
        );
      }
      
      if (existingPhoneData && existingPhoneData.length > 0) {
        return NextResponse.json(
          { message: 'You have already submitted a demo request with this phone number.' },
          { status: 200 }
        );
      }
    }
    
    // Format the time properly
    const { hour, minute, period } = data.preferredTime;
    let formattedHour = parseInt(hour);
    
    if (period === "PM" && formattedHour < 12) {
      formattedHour += 12;
    } else if (period === "AM" && formattedHour === 12) {
      formattedHour = 0;
    }
    
    const formattedTime = `${formattedHour.toString().padStart(2, "0")}:${minute}`;
    
    // Process the date to handle any timezone issues
    let preferredDate = data.preferredDate;
    
    // Handle if date is an ISO string (from our fix in the frontend)
    if (typeof preferredDate === 'string' && preferredDate.includes('T')) {
      // Extract just the date part from ISO string
      preferredDate = preferredDate.split('T')[0];
    }

    console.log('Processing date:', {
      original: data.preferredDate,
      processed: preferredDate
    });
    
    // Insert into demo_requests table
    const { error } = await supabase
      .from('demo_requests')
      .insert([
        {
          full_name: data.fullName,
          email: data.email,
          role: data.role,
          phone: data.phone,
          demo_type: data.demoType,
          preferred_date: preferredDate,
          preferred_time: formattedTime,
          additional_info: data.additionalInfo || null,
          created_at: new Date().toISOString()
        }
      ]);

    if (error) {
      console.error('Error inserting demo request:', error);
      return NextResponse.json(
        { error: 'Failed to submit demo request: ' + error.message },
        { status: 500 }
      );
    }

    return NextResponse.json(
      { success: true, message: 'Demo request submitted successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error processing demo request:', error);
    return NextResponse.json(
      { error: 'Internal server error: ' + (error instanceof Error ? error.message : String(error)) },
      { status: 500 }
    );
  }
}
