"use client";
// import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { TrendingUp, Clock, DollarSign, Heart } from "lucide-react";

const BenefitsSection = () => {
  const benefits = [
    {
      icon: <TrendingUp className="h-8 w-8 text-[#6A8E99]" />,
      title: "Improved Patient Outcomes",
      description: "AI-driven insights lead to better diagnosis accuracy and treatment effectiveness",
      metric: "25% improvement in patient satisfaction scores"
    },
    {
      icon: <Clock className="h-8 w-8 text-[#6A8E99]" />,
      title: "Operational Efficiency",
      description: "Streamline workflows and reduce administrative burden on healthcare staff",
      metric: "40% reduction in documentation time"
    },
    {
      icon: <DollarSign className="h-8 w-8 text-[#6A8E99]" />,
      title: "Cost Reduction",
      description: "Optimize resource allocation and reduce unnecessary procedures and readmissions",
      metric: "30% decrease in operational costs"
    },
    {
      icon: <Heart className="h-8 w-8 text-[#6A8E99]" />,
      title: "Enhanced Care Quality",
      description: "Provide personalized care plans and proactive health monitoring for patients",
      metric: "50% reduction in preventable complications"
    }
  ];

  const caseStudies = [
    {
      institution: "Regional Medical Center",
      type: "500-bed Hospital",
      challenge: "High readmission rates and inefficient patient monitoring",
      solution: "Implemented P.E.T.A.L.S. AI for predictive analytics and patient tracking",
      results: [
        "35% reduction in 30-day readmissions",
        "20% improvement in patient satisfaction",
        "$2.5M annual cost savings"
      ]
    },
    {
      institution: "Community Health Network",
      type: "Multi-clinic System",
      challenge: "Fragmented patient data and inconsistent care protocols",
      solution: "Deployed enterprise AI platform across 15 clinic locations",
      results: [
        "Unified patient records across all locations",
        "45% faster diagnosis times",
        "60% improvement in care coordination"
      ]
    }
  ];

  return (
    <section className="py-16 bg-[#F8FFFE]">
      <div className="container mx-auto px-4">
        {/* Benefits Grid */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Measurable Impact on Your Institution
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Healthcare institutions using P.E.T.A.L.S. AI see significant improvements 
              in patient outcomes, operational efficiency, and cost management.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {benefits.map((benefit, index) => (
              <Card key={index} className="bg-white border-0 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-8">
                  <div className="flex items-start gap-4">
                    <div className="bg-[#EAF7F9] p-3 rounded-lg flex-shrink-0">
                      {benefit.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-[#2E475D] mb-2">
                        {benefit.title}
                      </h3>
                      <p className="text-gray-600 mb-3">
                        {benefit.description}
                      </p>
                      <div className="bg-[#F0F9FF] px-3 py-2 rounded-md">
                        <span className="text-sm font-medium text-[#6A8E99]">
                          {benefit.metric}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Case Studies */}
        <div>
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Success Stories
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              See how healthcare institutions are transforming their operations with P.E.T.A.L.S. AI
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
            {caseStudies.map((study, index) => (
              <Card key={index} className="bg-white border-0 shadow-sm">
                <CardContent className="p-8">
                  <div className="mb-4">
                    <h3 className="text-xl font-semibold text-[#2E475D] mb-1">
                      {study.institution}
                    </h3>
                    <span className="text-sm text-[#6A8E99] font-medium">
                      {study.type}
                    </span>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Challenge:</h4>
                    <p className="text-gray-600 text-sm">
                      {study.challenge}
                    </p>
                  </div>
                  
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-800 mb-2">Solution:</h4>
                    <p className="text-gray-600 text-sm">
                      {study.solution}
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-gray-800 mb-2">Results:</h4>
                    <ul className="space-y-1">
                      {study.results.map((result, resultIndex) => (
                        <li key={resultIndex} className="flex items-start gap-2">
                          <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                          <span className="text-sm text-gray-600">{result}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
