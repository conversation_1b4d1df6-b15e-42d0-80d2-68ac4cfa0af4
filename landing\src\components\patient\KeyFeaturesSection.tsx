"use client";
import { TestTube, <PERSON>, Plus } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <TestTube className="h-12 w-12 text-[#6A8E99]" />,
      title: "Symptom Contextualizer",
      description: "Understand symptoms with real-time, AI-powered context and suggestions"
    },
    {
      icon: <Brain className="h-12 w-12 text-[#6A8E99]" />,
      title: "Pain & Fatigue AI Companion",
      description: "Daily check-ins to help manage chronic discomfort and track patterns"
    },
    {
      icon: <Plus className="h-12 w-12 text-[#6A8E99]" />,
      title: "Medication & Supplement Optimizer",
      description: "AI insights to optimize treatment plans and enhance safety"
    }
  ];

  return (
    <section id="features" className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Key Features
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Discover the powerful features that make P.E.T.A.L.S. AI your perfect health companion.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {features.map((feature, index) => (
            <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-8 text-center">
                <div className="mb-6 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#2E475D] mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default KeyFeaturesSection; 