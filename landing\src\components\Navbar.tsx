"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button"; // Adjust path if your ui folder is elsewhere
// import { useRouter } from "next/navigation";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>et<PERSON><PERSON><PERSON>,
  SheetClose,
} from "@/components/ui/sheet"; // Adjust path
import {
  Menubar,
  MenubarMenu,
  MenubarTrigger,
  MenubarContent,
  MenubarItem,
} from "@/components/ui/menubar";
import { Menu, X, ChevronDown } from "lucide-react";

interface NavLink {
  href: string;
  label: string;
}

const navLinks: NavLink[] = [
  { href: "/", label: "Home" },
  { href: "/provider", label: "For Providers" },
  { href: "/institution", label: "For Institutions" },
  { href: "/about_us", label: "About us" },
  { href: "/blog", label: "Blog" },
  { href: "/about_us#contact", label: "Support" },
];

const featureLinks = [
  { href: "/#features", label: "Patient Features" },
  { href: "/provider#features", label: "Provider Features" },
  { href: "/institution#features", label: "Institution Features" },
];

const faqLinks = [
  { href: "/faq", label: "General FAQ" },
  { href: "/patient/faq", label: "Patient FAQ" },
  { href: "/provider/faq", label: "Provider FAQ" },
  { href: "/institution/faq", label: "Institution FAQ" },
];

const waitlistLinks = [
  { href: "/early_access?type=patient", label: "Join as Patient" },
  { href: "/early_access?type=provider", label: "Join as Provider" },
  { href: "/early_access?type=institution", label: "Join as Institution" },
];

const Navbar = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  // const router = useRouter();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between m-auto px-4 md:px-6">
        {/* Logo */}
        <Link href="/" className="flex items-center gap-2">
          <Image
            src="images/logo_alone.svg" // Replace with your actual logo path in /public
            alt="Petals Ai Logo"
            width={40} // Adjust as needed
            height={40} // Adjust as needed
            className="h-8 w-auto md:h-10" // Responsive logo size
          />
          <span className="font-semibold text-lg md:text-xl text-[#2E475D]">
            {" "}
            {/* Approx color from image */}
            Petals Ai
          </span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center gap-6 text-sm font-medium text-[#2E475D]">
          {navLinks.map((link) => {
            if (link.label === "Features") {
              // This condition will no longer be met
              return null; // The "Features" link has been removed from navLinks
            }
            return (
              <Link
                key={link.label}
                href={link.href}
                className="hover:text-primary transition-colors"
              >
                {link.label}
              </Link>
            );
          })}
          <Menubar className="h-auto p-0 border-none bg-transparent">
            <MenubarMenu>
              <MenubarTrigger className="flex items-center gap-1 hover:text-primary transition-colors cursor-pointer">
                Features
                <ChevronDown className="h-4 w-4" />
              </MenubarTrigger>
              <MenubarContent>
                {featureLinks.map((link) => (
                  <MenubarItem key={link.label} asChild>
                    <Link href={link.href}>{link.label}</Link>
                  </MenubarItem>
                ))}
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
          <Menubar className="h-auto p-0 border-none bg-transparent">
            <MenubarMenu>
              <MenubarTrigger className="flex items-center gap-1 hover:text-primary transition-colors cursor-pointer">
                FAQ
                <ChevronDown className="h-4 w-4" />
              </MenubarTrigger>
              <MenubarContent>
                {faqLinks.map((link) => (
                  <MenubarItem key={link.label} asChild>
                    <Link href={link.href}>{link.label}</Link>
                  </MenubarItem>
                ))}
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
          {/* Join Waitlist Dropdown */}
          <Menubar className="h-auto p-0 border-none bg-transparent">
            <MenubarMenu>
              <MenubarTrigger
                className="flex items-center gap-1 text-white bg-[#6A8E99] hover:bg-[#597984] hover:text-white rounded-lg px-6 py-2 transition-colors cursor-pointer h-9"
              >
                Join Waitlist
                <ChevronDown className="h-4 w-4" />
              </MenubarTrigger>
              <MenubarContent>
                {waitlistLinks.map((link) => (
                  <MenubarItem key={link.label} asChild>
                    <Link href={link.href}>{link.label}</Link>
                  </MenubarItem>
                ))}
              </MenubarContent>
            </MenubarMenu>
          </Menubar>
        </nav>

        {/* Desktop CTA Button (now integrated into nav) */}
        <div className="hidden md:flex items-center">
          {/* This div is now effectively empty or can be removed if no other elements are needed */}
        </div>

        {/* Mobile Menu Button */}
        <div className="md:hidden">
          <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <Menu className="h-6 w-6 text-[#2E475D]" />
                <span className="sr-only">Toggle navigation menu</span>
              </Button>
            </SheetTrigger>
            <SheetContent
              side="right"
              className="w-full max-w-xs sm:max-w-sm bg-background p-0"
              hideCloseButton={true}
            >
              <SheetHeader className="p-3.5 border-b">
                <div className="flex items-center justify-end">
                  <SheetTitle className="text-xl font-semibold text-[#2E475D] sr-only">
                    Navigation Menu
                  </SheetTitle>
                  <SheetClose asChild>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="rounded-full"
                    >
                      <X className="h-5 w-5 text-[#2E475D]" />
                      <span className="sr-only">Close menu</span>
                    </Button>
                  </SheetClose>
                </div>
              </SheetHeader>
              <div className="p-6 space-y-4">
                <nav className="grid gap-4 text-[#2E475D]">
                  {navLinks.map((link) => {
                    return (
                      <Link
                        key={link.label}
                        href={link.href}
                        className="text-lg font-medium hover:text-primary transition-colors py-2"
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        {link.label}
                      </Link>
                    );
                  })}
                  <div>
                    <div className="flex items-center justify-between text-lg font-medium hover:text-primary transition-colors py-2 cursor-pointer">
                      Features
                    </div>
                    <div className="ml-4 flex flex-col">
                      {featureLinks.map((link) => (
                        <Link
                          key={link.label}
                          href={link.href}
                          className="text-base text-gray-700 hover:text-primary transition-colors py-1"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {link.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div className="flex items-center justify-between text-lg font-medium hover:text-primary transition-colors py-2 cursor-pointer">
                      FAQ
                    </div>
                    <div className="ml-4 flex flex-col">
                      {faqLinks.map((link) => (
                        <Link
                          key={link.label}
                          href={link.href}
                          className="text-base text-gray-700 hover:text-primary transition-colors py-1"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {link.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                  {/* Mobile Join Waitlist Dropdown */}
                  <div>
                    <div className="flex items-center justify-between text-lg font-medium hover:text-primary transition-colors py-2 cursor-pointer">
                      Join Waitlist
                    </div>
                    <div className="ml-4 flex flex-col">
                      {waitlistLinks.map((link) => (
                        <Link
                          key={link.label}
                          href={link.href}
                          className="text-base text-gray-700 hover:text-primary transition-colors py-1"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {link.label}
                        </Link>
                      ))}
                    </div>
                  </div>
                </nav>
                {/* Mobile CTA Button (now integrated into nav) */}
                {/* This button is now effectively removed as it's replaced by the dropdown */}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
