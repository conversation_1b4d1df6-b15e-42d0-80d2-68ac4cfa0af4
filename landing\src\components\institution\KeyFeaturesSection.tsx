"use client";
import { Building2, <PERSON>, Bar<PERSON>hart3, Shield, Stethoscope, Database } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const KeyFeaturesSection = () => {
  const features = [
    {
      icon: <Building2 className="h-12 w-12 text-[#6A8E99]" />,
      title: "Enterprise Integration",
      description: "Seamlessly integrate with existing EHR systems, hospital management software, and clinical workflows"
    },
    {
      icon: <Users className="h-12 w-12 text-[#6A8E99]" />,
      title: "Multi-User Management",
      description: "Comprehensive user management for healthcare teams, departments, and patient populations"
    },
    {
      icon: <BarChart3 className="h-12 w-12 text-[#6A8E99]" />,
      title: "Advanced Analytics",
      description: "Population health insights, outcome tracking, and predictive analytics for better decision-making"
    },
    {
      icon: <Shield className="h-12 w-12 text-[#6A8E99]" />,
      title: "Enterprise Security",
      description: "HIPAA-compliant infrastructure with advanced security protocols and audit trails"
    },
    {
      icon: <Stethoscope className="h-12 w-12 text-[#6A8E99]" />,
      title: "Clinical Decision Support",
      description: "AI-powered clinical insights and recommendations to support healthcare providers"
    },
    {
      icon: <Database className="h-12 w-12 text-[#6A8E99]" />,
      title: "Data Management",
      description: "Centralized patient data management with real-time synchronization and backup"
    }
  ];

  return (
    <section id="features" className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Enterprise-Grade Healthcare AI
          </h2>
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Comprehensive AI solutions designed specifically for healthcare institutions,
            from small clinics to large hospital systems.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {features.map((feature, index) => (
            <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
              <CardContent className="p-8 text-center">
                <div className="mb-6 flex justify-center">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-semibold text-[#2E475D] mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-16 bg-[#F8FFFE] rounded-2xl p-8 sm:p-12">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">500+</div>
              <div className="text-gray-600">Healthcare Providers</div>
            </div>
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">1M+</div>
              <div className="text-gray-600">Patients Served</div>
            </div>
            <div>
              <div className="text-3xl sm:text-4xl font-bold text-[#6A8E99] mb-2">99.9%</div>
              <div className="text-gray-600">Uptime Guarantee</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default KeyFeaturesSection;
