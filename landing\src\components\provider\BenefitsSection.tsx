"use client";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { TrendingUp, Clock, Shield, Users, Brain, BarChart3 } from "lucide-react";
import Link from "next/link";

const BenefitsSection = () => {
  const benefits = [
    {
      icon: <TrendingUp className="h-8 w-8 text-[#6A8E99]" />,
      title: "Improved Patient Outcomes",
      description: "AI-powered insights help you make more informed decisions, leading to better patient care and treatment success rates."
    },
    {
      icon: <Clock className="h-8 w-8 text-[#6A8E99]" />,
      title: "Time Efficiency",
      description: "Streamline your workflow with automated documentation, smart scheduling, and AI-assisted clinical decision support."
    },
    {
      icon: <Shield className="h-8 w-8 text-[#6A8E99]" />,
      title: "Enhanced Patient Safety",
      description: "Real-time monitoring and predictive analytics help identify potential issues before they become critical."
    },
    {
      icon: <Users className="h-8 w-8 text-[#6A8E99]" />,
      title: "Better Patient Engagement",
      description: "Keep patients actively involved in their care with personalized health insights and easy communication tools."
    },
    {
      icon: <Brain className="h-8 w-8 text-[#6A8E99]" />,
      title: "AI-Powered Clinical Support",
      description: "Access intelligent recommendations and evidence-based insights to support your clinical decision-making."
    },
    {
      icon: <BarChart3 className="h-8 w-8 text-[#6A8E99]" />,
      title: "Data-Driven Practice Management",
      description: "Comprehensive analytics and reporting tools help you optimize your practice operations and patient care."
    }
  ];

  const whyChoosePetals = [
    "Built specifically for healthcare providers by healthcare professionals",
    "HIPAA-compliant and enterprise-grade security",
    "Seamless integration with existing EHR systems",
    "Dedicated support team with clinical expertise",
    "Continuous AI learning from real-world healthcare data"
  ];

  return (
    <section className="py-16 sm:py-20 md:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Benefits Grid */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
              Transform Your Practice with P.E.T.A.L.S. AI
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Discover how our AI-powered platform can enhance your patient care, 
              streamline operations, and improve clinical outcomes.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {benefits.map((benefit, index) => (
              <Card key={index} className="bg-white border border-gray-100 shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardContent className="p-6">
                  <div className="mb-4">
                    {benefit.icon}
                  </div>
                  <h3 className="text-xl font-semibold text-[#2E475D] mb-3">
                    {benefit.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Why Choose P.E.T.A.L.S. Section */}
        <div className="bg-[#F2FFF6] rounded-2xl p-8 sm:p-12">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-2xl sm:text-3xl font-bold text-[#2E475D] mb-6">
              Why Healthcare Providers Choose P.E.T.A.L.S. AI
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              {whyChoosePetals.map((reason, index) => (
                <div key={index} className="flex items-start gap-3 text-left">
                  <div className="w-2 h-2 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-700">{reason}</p>
                </div>
              ))}
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/demo_request">
                <Button className="bg-[#6A8E99] hover:bg-[#597984] text-white px-8 py-3 rounded-md">
                  Request a Demo
                </Button>
              </Link>
              <Link href="/early_access">
                <Button variant="outline" className="border-[#6A8E99] text-[#6A8E99] hover:bg-[#EEF6F9] px-8 py-3 rounded-md">
                  Join Provider Beta
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
