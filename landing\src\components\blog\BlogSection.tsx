import Image from "next/image";
import Link from "next/link";

import { allBlogPosts } from "@/lib/blogData";

const BlogSection = () => {
  return (
    <section className="py-12 md:py-20 bg-gray-50">
      <div className="container mx-auto px-4 md:px-6">
        <h2 className="text-3xl md:text-4xl font-bold text-center text-[#2E475D] mb-12">
          Blogs
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {allBlogPosts.map((post, index) => (
            <div
              key={index}
              className="bg-white rounded-lg shadow-lg overflow-hidden transition-transform transform hover:scale-105"
            >
              <Image
                src={post.image}
                alt={post.title}
                width={500}
                height={300}
                layout="responsive"
                objectFit="cover"
                className="w-full h-48 md:h-56 object-cover"
              />
              <div className="p-6">
                <h3 className="text-xl font-semibold text-[#2E475D] mb-3">
                  {post.title}
                </h3>
                <p className="text-gray-600 mb-4">{post.excerpt}</p>
                <Link href={`/blog/${post.slug}`} className="text-[#6A8E99] hover:underline font-medium">
                  Read more
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogSection;