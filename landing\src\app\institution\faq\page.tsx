"use client";

import React from 'react';
import {
  Accordion,
  AccordionContent,
  Accordion<PERSON>tem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import Footer from "@/components/Footer"; // Import the Footer component

interface FAQItem {
  question: string;
  answer: string;
}

const institutionFaqs: FAQItem[] = [
  {
    question: "How can Petals AI benefit our healthcare institution?",
    answer: "Petals AI can optimize institutional operations, enhance diagnostic capabilities, improve patient management, and provide valuable data insights for better decision-making across your organization."
  },
  {
    question: "What scale of institutions can use Petals AI?",
    answer: "Petals AI is scalable and designed to cater to a wide range of healthcare institutions, from small clinics to large hospital networks. Our solutions are customizable to fit your specific needs and infrastructure."
  },
  {
    question: "How does Petals AI ensure data security and compliance for institutions?",
    answer: "We adhere to the highest standards of data security, including HIPAA, GDPR, and other relevant regulations. Our platform uses advanced encryption, access controls, and regular audits to protect sensitive patient and institutional data."
  },
  {
    question: "What kind of integration support does Petals AI offer?",
    answer: "Petals AI provides comprehensive integration support, including APIs for EHR systems, dedicated technical teams, and custom development options to ensure seamless compatibility with your existing IT infrastructure."
  },
  {
    question: "Can Petals AI help with workforce efficiency and resource allocation?",
    answer: "Yes, by automating repetitive tasks, providing predictive analytics for patient flow, and optimizing scheduling, Petals AI can significantly improve workforce efficiency and aid in better resource allocation within your institution."
  },
  {
    question: "What is the implementation process for Petals AI in an institution?",
    answer: "Our implementation process is collaborative, starting with a needs assessment, followed by a phased deployment, rigorous testing, and comprehensive training for your staff. We provide continuous support throughout and after implementation."
  }
];

export default function InstitutionFAQPage() {
  return (
    <>
      <div className="min-h-screen bg-[#EAF7F9] py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto bg-white p-8 rounded-lg shadow-md">
          <h1 className="text-4xl font-bold text-center text-[#2E475D] mb-10">
            Institution Frequently Asked Questions
          </h1>
          <div className="space-y-6">
            {institutionFaqs.map((faq, index) => (
              <Accordion key={index} type="single" collapsible className="w-full">
                <AccordionItem value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-lg font-semibold text-[#2E475D] hover:no-underline">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-gray-700 leading-relaxed px-4 py-2">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            ))}
          </div>
        </div>
      </div>
      <Footer />
    </>
  );
}